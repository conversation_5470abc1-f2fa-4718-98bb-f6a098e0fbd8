import 'package:flutter/material.dart';
import '../models/recipe.dart';

class RecipeSelectorDialog extends StatefulWidget {
  final List<Recipe> recipes;
  final MealType? mealType;

  const RecipeSelectorDialog({super.key, required this.recipes, this.mealType});

  @override
  State<RecipeSelectorDialog> createState() => _RecipeSelectorDialogState();
}

class _RecipeSelectorDialogState extends State<RecipeSelectorDialog> {
  String _searchQuery = '';
  MealType? _selectedMealType;

  @override
  void initState() {
    super.initState();
    _selectedMealType = widget.mealType;
  }

  List<Recipe> get _filteredRecipes {
    var filtered =
        widget.recipes.where((recipe) {
          final matchesSearch =
              recipe.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              recipe.description.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              recipe.tags.any(
                (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
              );

          final matchesMealType =
              _selectedMealType == null || recipe.mealType == _selectedMealType;

          return matchesSearch && matchesMealType;
        }).toList();

    // Sort by usage count (most used first) and then by name
    filtered.sort((a, b) {
      final usageComparison = b.usageCount.compareTo(a.usageCount);
      if (usageComparison != 0) return usageComparison;
      return a.name.compareTo(b.name);
    });

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    final filteredRecipes = _filteredRecipes;
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Responsive sizing for different screen sizes
    final isSmallScreen = screenHeight < 600 || screenWidth < 400;
    final dialogHeight =
        isSmallScreen ? screenHeight * 0.95 : screenHeight * 0.85;
    final headerPadding = isSmallScreen ? 12.0 : 16.0;
    final contentPadding = isSmallScreen ? 12.0 : 16.0;

    return Dialog(
      insetPadding: EdgeInsets.all(isSmallScreen ? 8 : 16),
      child: Container(
        width: screenWidth,
        height: dialogHeight,
        constraints: BoxConstraints(
          maxHeight: dialogHeight,
          maxWidth: screenWidth,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Theme.of(context).colorScheme.surface,
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(headerPadding),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      'Select Recipe',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ],
              ),
            ),

            // Content area with padding
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(contentPadding),
                child: Column(
                  children: [
                    // Search bar
                    SizedBox(
                      height: isSmallScreen ? 40 : 48,
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: 'Search recipes...',
                          prefixIcon: const Icon(Icons.search, size: 20),
                          border: const OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: isSmallScreen ? 4 : 8,
                          ),
                          isDense: isSmallScreen,
                        ),
                        onChanged: (value) {
                          setState(() {
                            _searchQuery = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 8 : 12),

                    // Meal type filter
                    SizedBox(
                      height: isSmallScreen ? 32 : 40,
                      child: ListView(
                        scrollDirection: Axis.horizontal,
                        children: [
                          FilterChip(
                            label: Text(
                              'All',
                              style: TextStyle(
                                fontSize: isSmallScreen ? 12 : 14,
                              ),
                            ),
                            selected: _selectedMealType == null,
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            onSelected: (selected) {
                              setState(() {
                                _selectedMealType =
                                    selected ? null : _selectedMealType;
                              });
                            },
                          ),
                          const SizedBox(width: 8),
                          ...MealType.values.map((mealType) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: FilterChip(
                                label: Text(
                                  _getMealTypeDisplayName(mealType),
                                  style: TextStyle(
                                    fontSize: isSmallScreen ? 12 : 14,
                                  ),
                                ),
                                selected: _selectedMealType == mealType,
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedMealType =
                                        selected ? mealType : null;
                                  });
                                },
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 8 : 12),

                    // Recipe list
                    Expanded(
                      child:
                          filteredRecipes.isEmpty
                              ? Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.restaurant_menu,
                                      size: isSmallScreen ? 48 : 64,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.3),
                                    ),
                                    SizedBox(height: isSmallScreen ? 12 : 16),
                                    Text(
                                      'No recipes found',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleMedium?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                    ),
                                    SizedBox(height: isSmallScreen ? 4 : 8),
                                    Text(
                                      'Try adjusting your search or filters',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.bodyMedium?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              )
                              : ListView.builder(
                                itemCount: filteredRecipes.length,
                                itemBuilder: (context, index) {
                                  final recipe = filteredRecipes[index];
                                  return Card(
                                    margin: EdgeInsets.only(
                                      bottom: isSmallScreen ? 4 : 8,
                                    ),
                                    child: ListTile(
                                      dense: isSmallScreen,
                                      title: Text(
                                        recipe.name,
                                        style: TextStyle(
                                          fontSize: isSmallScreen ? 14 : 16,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            recipe.description,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              fontSize: isSmallScreen ? 12 : 14,
                                            ),
                                          ),
                                          SizedBox(
                                            height: isSmallScreen ? 2 : 4,
                                          ),
                                          Wrap(
                                            spacing: isSmallScreen ? 8 : 16,
                                            children: [
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    Icons.access_time,
                                                    size:
                                                        isSmallScreen ? 12 : 14,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.6),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    '${recipe.totalTime} min',
                                                    style: TextStyle(
                                                      fontSize:
                                                          isSmallScreen
                                                              ? 11
                                                              : 12,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(
                                                    Icons.local_fire_department,
                                                    size:
                                                        isSmallScreen ? 12 : 14,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.6),
                                                  ),
                                                  const SizedBox(width: 4),
                                                  Text(
                                                    '${recipe.totalCalories.round()} cal',
                                                    style: TextStyle(
                                                      fontSize:
                                                          isSmallScreen
                                                              ? 11
                                                              : 12,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              if (recipe.usageCount > 0)
                                                Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Icon(
                                                      Icons.star,
                                                      size:
                                                          isSmallScreen
                                                              ? 12
                                                              : 14,
                                                      color: Colors.amber,
                                                    ),
                                                    const SizedBox(width: 4),
                                                    Text(
                                                      'Used ${recipe.usageCount}x',
                                                      style: TextStyle(
                                                        fontSize:
                                                            isSmallScreen
                                                                ? 11
                                                                : 12,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      trailing: Chip(
                                        label: Text(
                                          _getMealTypeDisplayName(
                                            recipe.mealType,
                                          ),
                                          style: TextStyle(
                                            fontSize: isSmallScreen ? 10 : 12,
                                          ),
                                        ),
                                        backgroundColor: _getMealTypeColor(
                                          recipe.mealType,
                                        ),
                                        materialTapTargetSize:
                                            MaterialTapTargetSize.shrinkWrap,
                                      ),
                                      onTap: () {
                                        Navigator.of(context).pop(recipe);
                                      },
                                    ),
                                  );
                                },
                              ),
                    ),

                    // Actions
                    SizedBox(height: isSmallScreen ? 8 : 16),
                    Row(
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        const Spacer(),
                        ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Clear Selection'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getMealTypeDisplayName(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast:
        return 'Breakfast';
      case MealType.lunch:
        return 'Lunch';
      case MealType.dinner:
        return 'Dinner';
      case MealType.snack:
        return 'Snack';
    }
  }

  Color _getMealTypeColor(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast:
        return Colors.orange.withValues(alpha: 0.2);
      case MealType.lunch:
        return Colors.blue.withValues(alpha: 0.2);
      case MealType.dinner:
        return Colors.purple.withValues(alpha: 0.2);
      case MealType.snack:
        return Colors.green.withValues(alpha: 0.2);
    }
  }
}
