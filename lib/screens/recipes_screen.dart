import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/meal_planner_service.dart';
import '../models/recipe.dart';
import 'recipe_form_screen.dart';
import 'recipe_detail_screen.dart';

class RecipesScreen extends StatefulWidget {
  const RecipesScreen({super.key});

  @override
  State<RecipesScreen> createState() => _RecipesScreenState();
}

class _RecipesScreenState extends State<RecipesScreen> {
  String _searchQuery = '';
  MealType? _selectedMealType;

  List<Recipe> _getFilteredRecipes(List<Recipe> recipes) {
    var filtered =
        recipes.where((recipe) {
          final matchesSearch =
              recipe.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              recipe.description.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              recipe.tags.any(
                (tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()),
              );

          final matchesMealType =
              _selectedMealType == null || recipe.mealType == _selectedMealType;

          return matchesSearch && matchesMealType;
        }).toList();

    // Sort by name
    filtered.sort((a, b) => a.name.compareTo(b.name));
    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Recipes'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<MealPlannerService>(
        builder: (context, service, child) {
          final filteredRecipes = _getFilteredRecipes(service.recipes);

          return Column(
            children: [
              // Search and filter section
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Search bar
                    TextField(
                      decoration: const InputDecoration(
                        hintText: 'Search recipes...',
                        prefixIcon: Icon(Icons.search),
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                    const SizedBox(height: 12),

                    // Meal type filter
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        children: [
                          FilterChip(
                            label: const Text('All'),
                            selected: _selectedMealType == null,
                            onSelected: (selected) {
                              setState(() {
                                _selectedMealType =
                                    selected ? null : _selectedMealType;
                              });
                            },
                          ),
                          const SizedBox(width: 8),
                          ...MealType.values.map((mealType) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: FilterChip(
                                label: Text(_getMealTypeDisplayName(mealType)),
                                selected: _selectedMealType == mealType,
                                onSelected: (selected) {
                                  setState(() {
                                    _selectedMealType =
                                        selected ? mealType : null;
                                  });
                                },
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Recipe list
              Expanded(
                child:
                    filteredRecipes.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.restaurant_menu,
                                size: 64,
                                color: Theme.of(
                                  context,
                                ).colorScheme.onSurface.withOpacity(0.3),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                service.recipes.isEmpty
                                    ? 'No recipes yet'
                                    : 'No recipes found',
                                style: Theme.of(
                                  context,
                                ).textTheme.titleMedium?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                service.recipes.isEmpty
                                    ? 'Add your first recipe to get started'
                                    : 'Try adjusting your search or filters',
                                style: Theme.of(
                                  context,
                                ).textTheme.bodyMedium?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: filteredRecipes.length,
                          itemBuilder: (context, index) {
                            final recipe = filteredRecipes[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              child: ListTile(
                                contentPadding: const EdgeInsets.all(16),
                                title: Text(
                                  recipe.name,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 4),
                                    Text(
                                      recipe.description,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Icon(
                                          Icons.access_time,
                                          size: 16,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.6),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${recipe.totalTime} min',
                                          style:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall,
                                        ),
                                        const SizedBox(width: 16),
                                        Icon(
                                          Icons.local_fire_department,
                                          size: 16,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.6),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${recipe.totalCalories.round()} cal',
                                          style:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall,
                                        ),
                                        const SizedBox(width: 16),
                                        Icon(
                                          Icons.restaurant,
                                          size: 16,
                                          color: Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.6),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          '${recipe.servings} servings',
                                          style:
                                              Theme.of(
                                                context,
                                              ).textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    Wrap(
                                      spacing: 4,
                                      runSpacing: 4,
                                      children: [
                                        Chip(
                                          label: Text(
                                            _getMealTypeDisplayName(
                                              recipe.mealType,
                                            ),
                                          ),
                                          backgroundColor: _getMealTypeColor(
                                            recipe.mealType,
                                          ),
                                          materialTapTargetSize:
                                              MaterialTapTargetSize.shrinkWrap,
                                        ),
                                        ...recipe.tags
                                            .take(2)
                                            .map(
                                              (tag) => Chip(
                                                label: Text(tag),
                                                backgroundColor: Colors.grey
                                                    .withOpacity(0.2),
                                                materialTapTargetSize:
                                                    MaterialTapTargetSize
                                                        .shrinkWrap,
                                              ),
                                            ),
                                      ],
                                    ),
                                  ],
                                ),
                                trailing: PopupMenuButton<String>(
                                  onSelected: (value) async {
                                    switch (value) {
                                      case 'edit':
                                        Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder:
                                                (context) => RecipeFormScreen(
                                                  recipe: recipe,
                                                ),
                                          ),
                                        );
                                        break;
                                      case 'delete':
                                        final confirmed = await showDialog<
                                          bool
                                        >(
                                          context: context,
                                          builder:
                                              (context) => AlertDialog(
                                                title: const Text(
                                                  'Delete Recipe',
                                                ),
                                                content: Text(
                                                  'Are you sure you want to delete "${recipe.name}"?',
                                                ),
                                                actions: [
                                                  TextButton(
                                                    onPressed:
                                                        () => Navigator.of(
                                                          context,
                                                        ).pop(false),
                                                    child: const Text('Cancel'),
                                                  ),
                                                  TextButton(
                                                    onPressed:
                                                        () => Navigator.of(
                                                          context,
                                                        ).pop(true),
                                                    child: const Text('Delete'),
                                                  ),
                                                ],
                                              ),
                                        );
                                        if (confirmed == true) {
                                          await service.deleteRecipe(recipe.id);
                                        }
                                        break;
                                    }
                                  },
                                  itemBuilder:
                                      (context) => [
                                        const PopupMenuItem(
                                          value: 'edit',
                                          child: ListTile(
                                            leading: Icon(Icons.edit),
                                            title: Text('Edit'),
                                            contentPadding: EdgeInsets.zero,
                                          ),
                                        ),
                                        const PopupMenuItem(
                                          value: 'delete',
                                          child: ListTile(
                                            leading: Icon(
                                              Icons.delete,
                                              color: Colors.red,
                                            ),
                                            title: Text(
                                              'Delete',
                                              style: TextStyle(
                                                color: Colors.red,
                                              ),
                                            ),
                                            contentPadding: EdgeInsets.zero,
                                          ),
                                        ),
                                      ],
                                ),
                                onTap: () {
                                  // TODO: Navigate to recipe detail screen
                                },
                              ),
                            );
                          },
                        ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const RecipeFormScreen()),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  String _getMealTypeDisplayName(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast:
        return 'Breakfast';
      case MealType.lunch:
        return 'Lunch';
      case MealType.dinner:
        return 'Dinner';
      case MealType.snack:
        return 'Snack';
    }
  }

  Color _getMealTypeColor(MealType mealType) {
    switch (mealType) {
      case MealType.breakfast:
        return Colors.orange.withValues(alpha: 0.2);
      case MealType.lunch:
        return Colors.blue.withValues(alpha: 0.2);
      case MealType.dinner:
        return Colors.purple.withValues(alpha: 0.2);
      case MealType.snack:
        return Colors.green.withValues(alpha: 0.2);
    }
  }
}
