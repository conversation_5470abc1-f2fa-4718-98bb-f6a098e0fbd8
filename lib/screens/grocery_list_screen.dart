import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/meal_planner_service.dart';
import '../models/grocery_item.dart';
import '../widgets/grocery_item_dialog.dart';

class GroceryListScreen extends StatefulWidget {
  const GroceryListScreen({super.key});

  @override
  State<GroceryListScreen> createState() => _GroceryListScreenState();
}

class _GroceryListScreenState extends State<GroceryListScreen> {
  bool _showCompleted = true;

  Map<String, List<GroceryItem>> _groupItemsByCategory(
    List<GroceryItem> items,
  ) {
    final Map<String, List<GroceryItem>> grouped = {};

    for (final item in items) {
      if (!grouped.containsKey(item.category)) {
        grouped[item.category] = [];
      }
      grouped[item.category]!.add(item);
    }

    // Sort items within each category
    for (final category in grouped.keys) {
      grouped[category]!.sort((a, b) {
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1; // Completed items go to bottom
        }
        return a.name.compareTo(b.name);
      });
    }

    return grouped;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Grocery List'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: Icon(
              _showCompleted ? Icons.visibility : Icons.visibility_off,
            ),
            onPressed: () {
              setState(() {
                _showCompleted = !_showCompleted;
              });
            },
            tooltip: _showCompleted ? 'Hide Completed' : 'Show Completed',
          ),
          PopupMenuButton<String>(
            onSelected: (value) async {
              final service = Provider.of<MealPlannerService>(
                context,
                listen: false,
              );
              switch (value) {
                case 'clear_completed':
                  final completedItems =
                      service.groceryItems
                          .where((item) => item.isCompleted)
                          .toList();
                  for (final item in completedItems) {
                    await service.deleteGroceryItem(item.id);
                  }
                  break;
                case 'clear_all':
                  final confirmed = await showDialog<bool>(
                    context: context,
                    builder:
                        (context) => AlertDialog(
                          title: const Text('Clear All Items'),
                          content: const Text(
                            'Are you sure you want to clear all grocery items?',
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(false),
                              child: const Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(true),
                              child: const Text('Clear All'),
                            ),
                          ],
                        ),
                  );
                  if (confirmed == true) {
                    final allItems = service.groceryItems.toList();
                    for (final item in allItems) {
                      await service.deleteGroceryItem(item.id);
                    }
                  }
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'clear_completed',
                    child: ListTile(
                      leading: Icon(Icons.clear),
                      title: Text('Clear Completed'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'clear_all',
                    child: ListTile(
                      leading: Icon(Icons.delete_sweep, color: Colors.red),
                      title: Text(
                        'Clear All',
                        style: TextStyle(color: Colors.red),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Consumer<MealPlannerService>(
        builder: (context, service, child) {
          final allItems = service.groceryItems;
          final filteredItems =
              _showCompleted
                  ? allItems
                  : allItems.where((item) => !item.isCompleted).toList();

          if (filteredItems.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 64,
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withOpacity(0.3),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    allItems.isEmpty
                        ? 'No grocery items yet'
                        : 'No items to show',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    allItems.isEmpty
                        ? 'Generate a list from your meal plans or add items manually'
                        : 'All items are completed',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(
                        context,
                      ).colorScheme.onSurface.withOpacity(0.6),
                    ),
                  ),
                ],
              ),
            );
          }

          final groupedItems = _groupItemsByCategory(filteredItems);
          final categories = groupedItems.keys.toList()..sort();

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: categories.length,
            itemBuilder: (context, categoryIndex) {
              final category = categories[categoryIndex];
              final items = groupedItems[category]!;

              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Category header
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primaryContainer,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(12),
                          topRight: Radius.circular(12),
                        ),
                      ),
                      child: Text(
                        category,
                        style: Theme.of(
                          context,
                        ).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color:
                              Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),

                    // Items in category
                    ...items.map((item) {
                      return ListTile(
                        leading: Checkbox(
                          value: item.isCompleted,
                          onChanged: (value) async {
                            await service.toggleGroceryItemCompleted(item.id);
                          },
                        ),
                        title: Text(
                          item.name,
                          style: TextStyle(
                            decoration:
                                item.isCompleted
                                    ? TextDecoration.lineThrough
                                    : null,
                            color:
                                item.isCompleted
                                    ? Theme.of(
                                      context,
                                    ).colorScheme.onSurface.withOpacity(0.6)
                                    : null,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${item.quantity} ${item.unit}',
                              style: TextStyle(
                                color:
                                    item.isCompleted
                                        ? Theme.of(
                                          context,
                                        ).colorScheme.onSurface.withOpacity(0.4)
                                        : Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withOpacity(0.7),
                              ),
                            ),
                            if (item.notes != null &&
                                item.notes!.isNotEmpty) ...[
                              const SizedBox(height: 2),
                              Text(
                                item.notes!,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontStyle: FontStyle.italic,
                                  color:
                                      item.isCompleted
                                          ? Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.4)
                                          : Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.6),
                                ),
                              ),
                            ],
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (item.estimatedPrice != null) ...[
                              Text(
                                '\$${item.estimatedPrice!.toStringAsFixed(2)}',
                                style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color:
                                      item.isCompleted
                                          ? Theme.of(context)
                                              .colorScheme
                                              .onSurface
                                              .withOpacity(0.4)
                                          : Theme.of(
                                            context,
                                          ).colorScheme.primary,
                                ),
                              ),
                              const SizedBox(width: 8),
                            ],
                            PopupMenuButton<String>(
                              onSelected: (value) async {
                                switch (value) {
                                  case 'edit':
                                    showDialog(
                                      context: context,
                                      builder:
                                          (context) => GroceryItemDialog(
                                            item: item,
                                            onSave: (updatedItem) async {
                                              await service.updateGroceryItem(
                                                updatedItem,
                                              );
                                            },
                                          ),
                                    );
                                    break;
                                  case 'delete':
                                    await service.deleteGroceryItem(item.id);
                                    break;
                                }
                              },
                              itemBuilder:
                                  (context) => [
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: ListTile(
                                        leading: Icon(Icons.edit),
                                        title: Text('Edit'),
                                        contentPadding: EdgeInsets.zero,
                                      ),
                                    ),
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: ListTile(
                                        leading: Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                        ),
                                        title: Text(
                                          'Delete',
                                          style: TextStyle(color: Colors.red),
                                        ),
                                        contentPadding: EdgeInsets.zero,
                                      ),
                                    ),
                                  ],
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder:
                (context) => GroceryItemDialog(
                  onSave: (item) async {
                    final service = Provider.of<MealPlannerService>(
                      context,
                      listen: false,
                    );
                    await service.addGroceryItem(item);
                  },
                ),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }
}
