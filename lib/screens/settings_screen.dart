import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/meal_planner_service.dart';
import '../services/auth_service.dart';
import '../models/dietary_preference.dart';
import 'dietary_preferences_screen.dart';
import 'user_profile_screen.dart';
import 'auth/login_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer2<MealPlannerService, AuthService>(
        builder: (context, mealService, authService, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // User Profile Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'User Profile',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (authService.currentUser != null) ...[
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              child: Text(
                                authService.currentUser!.name.isNotEmpty
                                    ? authService.currentUser!.name[0]
                                        .toUpperCase()
                                    : 'U',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    authService.currentUser!.name,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    authService.currentUser!.email,
                                    style: Theme.of(
                                      context,
                                    ).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurface
                                          .withValues(alpha: 0.7),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                      ],
                      Row(
                        children: [
                          ElevatedButton.icon(
                            onPressed: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (context) => const UserProfileScreen(),
                                ),
                              );
                            },
                            icon: const Icon(Icons.person),
                            label: const Text('Edit Profile'),
                          ),
                          const SizedBox(width: 16),
                          OutlinedButton.icon(
                            onPressed: () async {
                              final confirmed = await showDialog<bool>(
                                context: context,
                                builder:
                                    (context) => AlertDialog(
                                      title: const Text('Sign Out'),
                                      content: const Text(
                                        'Are you sure you want to sign out?',
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed:
                                              () => Navigator.of(
                                                context,
                                              ).pop(false),
                                          child: const Text('Cancel'),
                                        ),
                                        TextButton(
                                          onPressed:
                                              () => Navigator.of(
                                                context,
                                              ).pop(true),
                                          child: const Text('Sign Out'),
                                        ),
                                      ],
                                    ),
                              );

                              if (confirmed == true) {
                                await authService.signOut();
                                if (context.mounted) {
                                  Navigator.of(context).pushAndRemoveUntil(
                                    MaterialPageRoute(
                                      builder: (context) => const LoginScreen(),
                                    ),
                                    (route) => false,
                                  );
                                }
                              }
                            },
                            icon: const Icon(Icons.logout),
                            label: const Text('Sign Out'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Dietary Preferences Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Dietary Preferences',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      if (mealService.dietaryPreference != null) ...[
                        _buildDietaryPreferencesSummary(
                          mealService.dietaryPreference!,
                        ),
                        const SizedBox(height: 16),
                      ],
                      ElevatedButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder:
                                  (context) => const DietaryPreferencesScreen(),
                            ),
                          );
                        },
                        icon: Icon(
                          mealService.dietaryPreference != null
                              ? Icons.edit
                              : Icons.add,
                        ),
                        label: Text(
                          mealService.dietaryPreference != null
                              ? 'Edit Preferences'
                              : 'Set Preferences',
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // App Statistics Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Statistics',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildStatisticRow(
                        'Total Recipes',
                        '${mealService.recipes.length}',
                      ),
                      _buildStatisticRow(
                        'Meal Plans',
                        '${mealService.mealPlans.length}',
                      ),
                      _buildStatisticRow(
                        'Grocery Items',
                        '${mealService.groceryItems.length}',
                      ),
                      _buildStatisticRow(
                        'Completed Items',
                        '${mealService.groceryItems.where((item) => item.isCompleted).length}',
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // About Section
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'About',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      ListTile(
                        leading: const Icon(Icons.info),
                        title: const Text('Version'),
                        subtitle: const Text('1.0.0'),
                        contentPadding: EdgeInsets.zero,
                      ),
                      ListTile(
                        leading: const Icon(Icons.help),
                        title: const Text('Help & Support'),
                        contentPadding: EdgeInsets.zero,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Help feature coming soon!'),
                            ),
                          );
                        },
                      ),
                      ListTile(
                        leading: const Icon(Icons.star),
                        title: const Text('Rate App'),
                        contentPadding: EdgeInsets.zero,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Rating feature coming soon!'),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDietaryPreferencesSummary(DietaryPreference preference) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (preference.restrictions.isNotEmpty) ...[
          Text(
            'Dietary Restrictions:',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children:
                preference.restrictions.map((restriction) {
                  return Chip(
                    label: Text(_getDietaryRestrictionDisplayName(restriction)),
                    backgroundColor: Colors.green.withValues(alpha: 0.2),
                  );
                }).toList(),
          ),
          const SizedBox(height: 8),
        ],
        if (preference.dailyCalorieGoal != null) ...[
          Text(
            'Daily Calorie Goal: ${preference.dailyCalorieGoal} calories',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 4),
        ],
        if (preference.allergies.isNotEmpty) ...[
          Text(
            'Allergies: ${preference.allergies.join(', ')}',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ],
    );
  }

  Widget _buildStatisticRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  String _getDietaryRestrictionDisplayName(DietaryRestriction restriction) {
    switch (restriction) {
      case DietaryRestriction.vegetarian:
        return 'Vegetarian';
      case DietaryRestriction.vegan:
        return 'Vegan';
      case DietaryRestriction.glutenFree:
        return 'Gluten-Free';
      case DietaryRestriction.dairyFree:
        return 'Dairy-Free';
      case DietaryRestriction.nutFree:
        return 'Nut-Free';
      case DietaryRestriction.lowCarb:
        return 'Low Carb';
      case DietaryRestriction.lowFat:
        return 'Low Fat';
      case DietaryRestriction.lowSodium:
        return 'Low Sodium';
      case DietaryRestriction.keto:
        return 'Keto';
      case DietaryRestriction.paleo:
        return 'Paleo';
      case DietaryRestriction.mediterranean:
        return 'Mediterranean';
      case DietaryRestriction.diabetic:
        return 'Diabetic';
      case DietaryRestriction.halal:
        return 'Halal';
      case DietaryRestriction.kosher:
        return 'Kosher';
    }
  }
}
