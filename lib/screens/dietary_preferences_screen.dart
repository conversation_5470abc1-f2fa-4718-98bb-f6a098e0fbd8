import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import '../models/dietary_preference.dart';
import '../services/meal_planner_service.dart';
import '../services/auth_service.dart';

class DietaryPreferencesScreen extends StatefulWidget {
  const DietaryPreferencesScreen({super.key});

  @override
  State<DietaryPreferencesScreen> createState() => _DietaryPreferencesScreenState();
}

class _DietaryPreferencesScreenState extends State<DietaryPreferencesScreen> {
  final _formKey = GlobalKey<FormState>();
  final _dailyCalorieGoalController = TextEditingController();
  final _allergyController = TextEditingController();
  final _dislikedController = TextEditingController();
  final _preferredController = TextEditingController();

  Set<DietaryRestriction> _selectedRestrictions = {};
  List<String> _allergies = [];
  List<String> _dislikedIngredients = [];
  List<String> _preferredIngredients = [];
  Map<String, double> _macroGoals = {};
  bool _isLoading = false;

  final Uuid _uuid = const Uuid();

  @override
  void initState() {
    super.initState();
    _loadExistingPreferences();
  }

  void _loadExistingPreferences() {
    final service = Provider.of<MealPlannerService>(context, listen: false);
    final existing = service.dietaryPreference;
    
    if (existing != null) {
      setState(() {
        _selectedRestrictions = existing.restrictions.toSet();
        _allergies = List.from(existing.allergies);
        _dislikedIngredients = List.from(existing.dislikedIngredients);
        _preferredIngredients = List.from(existing.preferredIngredients);
        _macroGoals = Map.from(existing.macroGoals ?? {});
        _dailyCalorieGoalController.text = existing.dailyCalorieGoal?.toString() ?? '';
      });
    }
  }

  @override
  void dispose() {
    _dailyCalorieGoalController.dispose();
    _allergyController.dispose();
    _dislikedController.dispose();
    _preferredController.dispose();
    super.dispose();
  }

  Future<void> _savePreferences() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final mealPlannerService = Provider.of<MealPlannerService>(context, listen: false);
      final now = DateTime.now();

      final preference = DietaryPreference(
        id: mealPlannerService.dietaryPreference?.id ?? _uuid.v4(),
        userId: authService.currentUser?.id ?? 'default',
        restrictions: _selectedRestrictions.toList(),
        allergies: _allergies,
        dislikedIngredients: _dislikedIngredients,
        preferredIngredients: _preferredIngredients,
        dailyCalorieGoal: _dailyCalorieGoalController.text.isNotEmpty 
            ? int.parse(_dailyCalorieGoalController.text) 
            : null,
        macroGoals: _macroGoals.isNotEmpty ? _macroGoals : null,
        createdAt: mealPlannerService.dietaryPreference?.createdAt ?? now,
        updatedAt: now,
      );

      await mealPlannerService.saveDietaryPreference(preference);

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Dietary preferences saved!')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving preferences: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _addAllergy() {
    if (_allergyController.text.trim().isNotEmpty) {
      setState(() {
        _allergies.add(_allergyController.text.trim());
        _allergyController.clear();
      });
    }
  }

  void _addDislikedIngredient() {
    if (_dislikedController.text.trim().isNotEmpty) {
      setState(() {
        _dislikedIngredients.add(_dislikedController.text.trim());
        _dislikedController.clear();
      });
    }
  }

  void _addPreferredIngredient() {
    if (_preferredController.text.trim().isNotEmpty) {
      setState(() {
        _preferredIngredients.add(_preferredController.text.trim());
        _preferredController.clear();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dietary Preferences'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_isLoading)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            )
          else
            TextButton(
              onPressed: _savePreferences,
              child: const Text('Save'),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Dietary Restrictions
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Dietary Restrictions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: DietaryRestriction.values.map((restriction) {
                        return FilterChip(
                          label: Text(_getDietaryRestrictionDisplayName(restriction)),
                          selected: _selectedRestrictions.contains(restriction),
                          onSelected: (selected) {
                            setState(() {
                              if (selected) {
                                _selectedRestrictions.add(restriction);
                              } else {
                                _selectedRestrictions.remove(restriction);
                              }
                            });
                          },
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Daily Calorie Goal
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Daily Calorie Goal',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: _dailyCalorieGoalController,
                      decoration: const InputDecoration(
                        labelText: 'Calories per day',
                        border: OutlineInputBorder(),
                        suffixText: 'cal',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          if (int.tryParse(value) == null || int.parse(value) < 500) {
                            return 'Please enter a valid calorie goal (minimum 500)';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Allergies
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Allergies',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _allergyController,
                            decoration: const InputDecoration(
                              labelText: 'Add allergy',
                              border: OutlineInputBorder(),
                            ),
                            onSubmitted: (_) => _addAllergy(),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: _addAllergy,
                        ),
                      ],
                    ),
                    if (_allergies.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        children: _allergies.map((allergy) {
                          return Chip(
                            label: Text(allergy),
                            onDeleted: () {
                              setState(() {
                                _allergies.remove(allergy);
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Disliked Ingredients
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Disliked Ingredients',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _dislikedController,
                            decoration: const InputDecoration(
                              labelText: 'Add disliked ingredient',
                              border: OutlineInputBorder(),
                            ),
                            onSubmitted: (_) => _addDislikedIngredient(),
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: _addDislikedIngredient,
                        ),
                      ],
                    ),
                    if (_dislikedIngredients.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Wrap(
                        spacing: 8,
                        children: _dislikedIngredients.map((ingredient) {
                          return Chip(
                            label: Text(ingredient),
                            onDeleted: () {
                              setState(() {
                                _dislikedIngredients.remove(ingredient);
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  String _getDietaryRestrictionDisplayName(DietaryRestriction restriction) {
    switch (restriction) {
      case DietaryRestriction.vegetarian:
        return 'Vegetarian';
      case DietaryRestriction.vegan:
        return 'Vegan';
      case DietaryRestriction.glutenFree:
        return 'Gluten-Free';
      case DietaryRestriction.dairyFree:
        return 'Dairy-Free';
      case DietaryRestriction.nutFree:
        return 'Nut-Free';
      case DietaryRestriction.lowCarb:
        return 'Low Carb';
      case DietaryRestriction.lowFat:
        return 'Low Fat';
      case DietaryRestriction.lowSodium:
        return 'Low Sodium';
      case DietaryRestriction.keto:
        return 'Keto';
      case DietaryRestriction.paleo:
        return 'Paleo';
      case DietaryRestriction.mediterranean:
        return 'Mediterranean';
      case DietaryRestriction.diabetic:
        return 'Diabetic';
      case DietaryRestriction.halal:
        return 'Halal';
      case DietaryRestriction.kosher:
        return 'Kosher';
    }
  }
}
