import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../services/meal_planner_service.dart';
import '../models/recipe.dart';
import '../widgets/meal_card.dart';
import '../widgets/recipe_selector_dialog.dart';

class WeeklyPlannerScreen extends StatefulWidget {
  const WeeklyPlannerScreen({super.key});

  @override
  State<WeeklyPlannerScreen> createState() => _WeeklyPlannerScreenState();
}

class _WeeklyPlannerScreenState extends State<WeeklyPlannerScreen> {
  DateTime _currentWeekStart = DateTime.now();

  @override
  void initState() {
    super.initState();
    _currentWeekStart = _getWeekStart(DateTime.now());
  }

  DateTime _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }

  List<DateTime> _getWeekDates(DateTime weekStart) {
    return List.generate(7, (index) => weekStart.add(Duration(days: index)));
  }

  void _previousWeek() {
    setState(() {
      _currentWeekStart = _currentWeekStart.subtract(const Duration(days: 7));
    });
  }

  void _nextWeek() {
    setState(() {
      _currentWeekStart = _currentWeekStart.add(const Duration(days: 7));
    });
  }

  void _goToToday() {
    setState(() {
      _currentWeekStart = _getWeekStart(DateTime.now());
    });
  }

  Future<void> _selectMeal(DateTime date, MealType mealType) async {
    final service = Provider.of<MealPlannerService>(context, listen: false);
    final selectedRecipe = await showDialog<Recipe>(
      context: context,
      builder:
          (context) => RecipeSelectorDialog(
            recipes: service.recipes,
            mealType: mealType,
          ),
    );

    if (selectedRecipe != null) {
      await service.setMealForDate(date, mealType, selectedRecipe);
    }
  }

  @override
  Widget build(BuildContext context) {
    final weekDates = _getWeekDates(_currentWeekStart);
    final weekEndDate = weekDates.last;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Meal Planner'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.today),
            onPressed: _goToToday,
            tooltip: 'Go to Today',
          ),
        ],
      ),
      body: Column(
        children: [
          // Week navigation
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: _previousWeek,
                ),
                Text(
                  '${DateFormat('MMM d').format(_currentWeekStart)} - ${DateFormat('MMM d, yyyy').format(weekEndDate)}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: _nextWeek,
                ),
              ],
            ),
          ),
          // Week view
          Expanded(
            child: Consumer<MealPlannerService>(
              builder: (context, service, child) {
                return ListView.builder(
                  itemCount: weekDates.length,
                  itemBuilder: (context, index) {
                    final date = weekDates[index];
                    final mealPlan = service.getMealPlanForDate(date);
                    final isToday = _isSameDay(date, DateTime.now());

                    return Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      color:
                          isToday
                              ? Theme.of(context).colorScheme.primaryContainer
                              : null,
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Date header
                            Row(
                              children: [
                                Text(
                                  DateFormat('EEEE, MMM d').format(date),
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color:
                                        isToday
                                            ? Theme.of(
                                              context,
                                            ).colorScheme.primary
                                            : null,
                                  ),
                                ),
                                if (isToday) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      'Today',
                                      style: TextStyle(
                                        color:
                                            Theme.of(
                                              context,
                                            ).colorScheme.onPrimary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            const SizedBox(height: 12),
                            // Meals
                            Row(
                              children: [
                                Expanded(
                                  child: MealCard(
                                    title: 'Breakfast',
                                    recipe: mealPlan?.breakfast,
                                    onTap:
                                        () => _selectMeal(
                                          date,
                                          MealType.breakfast,
                                        ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: MealCard(
                                    title: 'Lunch',
                                    recipe: mealPlan?.lunch,
                                    onTap:
                                        () => _selectMeal(date, MealType.lunch),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: MealCard(
                                    title: 'Dinner',
                                    recipe: mealPlan?.dinner,
                                    onTap:
                                        () =>
                                            _selectMeal(date, MealType.dinner),
                                  ),
                                ),
                              ],
                            ),
                            // Snacks
                            if (mealPlan?.snacks.isNotEmpty == true) ...[
                              const SizedBox(height: 8),
                              Wrap(
                                spacing: 8,
                                children:
                                    mealPlan!.snacks.map((snack) {
                                      return Chip(
                                        label: Text(snack.name),
                                        onDeleted: () async {
                                          final updatedPlan = mealPlan
                                              .removeMeal(
                                                MealType.snack,
                                                snackToRemove: snack,
                                              );
                                          await service.addOrUpdateMealPlan(
                                            updatedPlan,
                                          );
                                        },
                                      );
                                    }).toList(),
                              ),
                            ],
                            // Add snack button
                            const SizedBox(height: 8),
                            TextButton.icon(
                              onPressed:
                                  () => _selectMeal(date, MealType.snack),
                              icon: const Icon(Icons.add),
                              label: const Text('Add Snack'),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          final service = Provider.of<MealPlannerService>(
            context,
            listen: false,
          );
          await service.generateGroceryListFromMealPlans(weekDates);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Grocery list generated from this week\'s meals!',
                ),
              ),
            );
          }
        },
        icon: const Icon(Icons.shopping_cart),
        label: const Text('Generate Grocery List'),
      ),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }
}
