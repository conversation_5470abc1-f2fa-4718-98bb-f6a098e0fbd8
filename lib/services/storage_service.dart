import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/recipe.dart';
import '../models/meal_plan.dart';
import '../models/grocery_item.dart';
import '../models/dietary_preference.dart';

class StorageService {
  static const String _recipesKey = 'recipes';
  static const String _mealPlansKey = 'meal_plans';
  static const String _groceryItemsKey = 'grocery_items';
  static const String _dietaryPreferencesKey = 'dietary_preferences';
  static const String _isFirstLaunchKey = 'is_first_launch';

  static SharedPreferences? _prefs;

  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  static SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('StorageService not initialized. Call StorageService.init() first.');
    }
    return _prefs!;
  }

  // First launch check
  static bool get isFirstLaunch {
    return prefs.getBool(_isFirstLaunchKey) ?? true;
  }

  static Future<void> setFirstLaunchComplete() async {
    await prefs.setBool(_isFirstLaunchKey, false);
  }

  // Recipe operations
  static Future<List<Recipe>> getRecipes() async {
    final String? recipesJson = prefs.getString(_recipesKey);
    if (recipesJson == null) return [];

    final List<dynamic> recipesList = jsonDecode(recipesJson);
    return recipesList
        .map((json) => Recipe.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  static Future<void> saveRecipes(List<Recipe> recipes) async {
    final String recipesJson = jsonEncode(recipes.map((r) => r.toJson()).toList());
    await prefs.setString(_recipesKey, recipesJson);
  }

  static Future<void> addRecipe(Recipe recipe) async {
    final recipes = await getRecipes();
    recipes.add(recipe);
    await saveRecipes(recipes);
  }

  static Future<void> updateRecipe(Recipe recipe) async {
    final recipes = await getRecipes();
    final index = recipes.indexWhere((r) => r.id == recipe.id);
    if (index != -1) {
      recipes[index] = recipe;
      await saveRecipes(recipes);
    }
  }

  static Future<void> deleteRecipe(String recipeId) async {
    final recipes = await getRecipes();
    recipes.removeWhere((r) => r.id == recipeId);
    await saveRecipes(recipes);
  }

  // Meal plan operations
  static Future<List<MealPlan>> getMealPlans() async {
    final String? mealPlansJson = prefs.getString(_mealPlansKey);
    if (mealPlansJson == null) return [];

    final List<dynamic> mealPlansList = jsonDecode(mealPlansJson);
    return mealPlansList
        .map((json) => MealPlan.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  static Future<void> saveMealPlans(List<MealPlan> mealPlans) async {
    final String mealPlansJson = jsonEncode(mealPlans.map((mp) => mp.toJson()).toList());
    await prefs.setString(_mealPlansKey, mealPlansJson);
  }

  static Future<void> addMealPlan(MealPlan mealPlan) async {
    final mealPlans = await getMealPlans();
    final existingIndex = mealPlans.indexWhere((mp) => mp.id == mealPlan.id);
    if (existingIndex != -1) {
      mealPlans[existingIndex] = mealPlan;
    } else {
      mealPlans.add(mealPlan);
    }
    await saveMealPlans(mealPlans);
  }

  static Future<void> deleteMealPlan(String mealPlanId) async {
    final mealPlans = await getMealPlans();
    mealPlans.removeWhere((mp) => mp.id == mealPlanId);
    await saveMealPlans(mealPlans);
  }

  // Grocery item operations
  static Future<List<GroceryItem>> getGroceryItems() async {
    final String? groceryItemsJson = prefs.getString(_groceryItemsKey);
    if (groceryItemsJson == null) return [];

    final List<dynamic> groceryItemsList = jsonDecode(groceryItemsJson);
    return groceryItemsList
        .map((json) => GroceryItem.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  static Future<void> saveGroceryItems(List<GroceryItem> groceryItems) async {
    final String groceryItemsJson = jsonEncode(groceryItems.map((gi) => gi.toJson()).toList());
    await prefs.setString(_groceryItemsKey, groceryItemsJson);
  }

  static Future<void> addGroceryItem(GroceryItem groceryItem) async {
    final groceryItems = await getGroceryItems();
    groceryItems.add(groceryItem);
    await saveGroceryItems(groceryItems);
  }

  static Future<void> updateGroceryItem(GroceryItem groceryItem) async {
    final groceryItems = await getGroceryItems();
    final index = groceryItems.indexWhere((gi) => gi.id == groceryItem.id);
    if (index != -1) {
      groceryItems[index] = groceryItem;
      await saveGroceryItems(groceryItems);
    }
  }

  static Future<void> deleteGroceryItem(String groceryItemId) async {
    final groceryItems = await getGroceryItems();
    groceryItems.removeWhere((gi) => gi.id == groceryItemId);
    await saveGroceryItems(groceryItems);
  }

  // Dietary preference operations
  static Future<DietaryPreference?> getDietaryPreference() async {
    final String? dietaryPreferenceJson = prefs.getString(_dietaryPreferencesKey);
    if (dietaryPreferenceJson == null) return null;

    final Map<String, dynamic> json = jsonDecode(dietaryPreferenceJson);
    return DietaryPreference.fromJson(json);
  }

  static Future<void> saveDietaryPreference(DietaryPreference dietaryPreference) async {
    final String dietaryPreferenceJson = jsonEncode(dietaryPreference.toJson());
    await prefs.setString(_dietaryPreferencesKey, dietaryPreferenceJson);
  }

  static Future<void> deleteDietaryPreference() async {
    await prefs.remove(_dietaryPreferencesKey);
  }

  // Clear all data
  static Future<void> clearAllData() async {
    await prefs.clear();
  }
}
