import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';
import '../models/user.dart';

class AuthService extends ChangeNotifier {
  static const String _usersKey = 'users';
  static const String _credentialsKey = 'user_credentials';
  static const String _currentUserKey = 'current_user';
  static const String _isLoggedInKey = 'is_logged_in';

  User? _currentUser;
  bool _isLoggedIn = false;
  bool _isLoading = false;
  String? _error;

  final Uuid _uuid = const Uuid();

  // Getters
  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize the service
  Future<void> initialize() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      _isLoggedIn = prefs.getBool(_isLoggedInKey) ?? false;
      
      if (_isLoggedIn) {
        final userJson = prefs.getString(_currentUserKey);
        if (userJson != null) {
          _currentUser = User.fromJson(jsonDecode(userJson));
        } else {
          _isLoggedIn = false;
          await prefs.setBool(_isLoggedInKey, false);
        }
      }
    } catch (e) {
      _setError('Failed to initialize auth: $e');
      _isLoggedIn = false;
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<bool> signUp({
    required String email,
    required String password,
    required String name,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check if user already exists
      final existingUsers = await _getUsers();
      if (existingUsers.any((user) => user.email.toLowerCase() == email.toLowerCase())) {
        _setError('User with this email already exists');
        return false;
      }

      // Create new user
      final userId = _uuid.v4();
      final now = DateTime.now();
      
      final user = User(
        id: userId,
        email: email.toLowerCase(),
        name: name,
        createdAt: now,
        updatedAt: now,
      );

      final credentials = UserCredentials(
        id: userId,
        email: email.toLowerCase(),
        passwordHash: _hashPassword(password),
        createdAt: now,
      );

      // Save user and credentials
      await _saveUser(user);
      await _saveCredentials(credentials);

      // Log in the user
      _currentUser = user;
      _isLoggedIn = true;
      
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to sign up: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _setError(null);

    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get user credentials
      final credentials = await _getCredentials();
      final userCredentials = credentials.firstWhere(
        (cred) => cred.email.toLowerCase() == email.toLowerCase(),
        orElse: () => throw Exception('User not found'),
      );

      // Verify password
      if (userCredentials.passwordHash != _hashPassword(password)) {
        _setError('Invalid email or password');
        return false;
      }

      // Get user data
      final users = await _getUsers();
      final user = users.firstWhere(
        (u) => u.id == userCredentials.id,
        orElse: () => throw Exception('User data not found'),
      );

      // Log in the user
      _currentUser = user;
      _isLoggedIn = true;
      
      await prefs.setBool(_isLoggedInKey, true);
      await prefs.setString(_currentUserKey, jsonEncode(user.toJson()));

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Invalid email or password');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _currentUser = null;
      _isLoggedIn = false;
      
      await prefs.setBool(_isLoggedInKey, false);
      await prefs.remove(_currentUserKey);

      notifyListeners();
    } catch (e) {
      _setError('Failed to sign out: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateProfile({
    String? name,
    String? profileImagePath,
    Map<String, dynamic>? preferences,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    try {
      final updatedUser = _currentUser!.copyWith(
        name: name ?? _currentUser!.name,
        profileImagePath: profileImagePath ?? _currentUser!.profileImagePath,
        preferences: preferences ?? _currentUser!.preferences,
        updatedAt: DateTime.now(),
      );

      await _updateUser(updatedUser);
      _currentUser = updatedUser;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_currentUserKey, jsonEncode(updatedUser.toJson()));

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Failed to update profile: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (_currentUser == null) return false;

    _setLoading(true);
    try {
      final credentials = await _getCredentials();
      final userCredentials = credentials.firstWhere(
        (cred) => cred.id == _currentUser!.id,
      );

      // Verify current password
      if (userCredentials.passwordHash != _hashPassword(currentPassword)) {
        _setError('Current password is incorrect');
        return false;
      }

      // Update password
      final updatedCredentials = UserCredentials(
        id: userCredentials.id,
        email: userCredentials.email,
        passwordHash: _hashPassword(newPassword),
        createdAt: userCredentials.createdAt,
      );

      await _updateCredentials(updatedCredentials);
      return true;
    } catch (e) {
      _setError('Failed to change password: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  Future<List<User>> _getUsers() async {
    final prefs = await SharedPreferences.getInstance();
    final usersJson = prefs.getString(_usersKey);
    if (usersJson == null) return [];

    final List<dynamic> usersList = jsonDecode(usersJson);
    return usersList.map((json) => User.fromJson(json as Map<String, dynamic>)).toList();
  }

  Future<void> _saveUser(User user) async {
    final users = await _getUsers();
    users.add(user);
    await _saveUsers(users);
  }

  Future<void> _updateUser(User user) async {
    final users = await _getUsers();
    final index = users.indexWhere((u) => u.id == user.id);
    if (index != -1) {
      users[index] = user;
      await _saveUsers(users);
    }
  }

  Future<void> _saveUsers(List<User> users) async {
    final prefs = await SharedPreferences.getInstance();
    final usersJson = jsonEncode(users.map((u) => u.toJson()).toList());
    await prefs.setString(_usersKey, usersJson);
  }

  Future<List<UserCredentials>> _getCredentials() async {
    final prefs = await SharedPreferences.getInstance();
    final credentialsJson = prefs.getString(_credentialsKey);
    if (credentialsJson == null) return [];

    final List<dynamic> credentialsList = jsonDecode(credentialsJson);
    return credentialsList.map((json) => UserCredentials.fromJson(json as Map<String, dynamic>)).toList();
  }

  Future<void> _saveCredentials(UserCredentials credentials) async {
    final credentialsList = await _getCredentials();
    credentialsList.add(credentials);
    await _saveCredentialsList(credentialsList);
  }

  Future<void> _updateCredentials(UserCredentials credentials) async {
    final credentialsList = await _getCredentials();
    final index = credentialsList.indexWhere((c) => c.id == credentials.id);
    if (index != -1) {
      credentialsList[index] = credentials;
      await _saveCredentialsList(credentialsList);
    }
  }

  Future<void> _saveCredentialsList(List<UserCredentials> credentialsList) async {
    final prefs = await SharedPreferences.getInstance();
    final credentialsJson = jsonEncode(credentialsList.map((c) => c.toJson()).toList());
    await prefs.setString(_credentialsKey, credentialsJson);
  }
}
