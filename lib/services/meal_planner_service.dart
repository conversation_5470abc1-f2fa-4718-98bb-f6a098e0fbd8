import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';
import '../models/recipe.dart';
import '../models/meal_plan.dart';
import '../models/grocery_item.dart';
import '../models/dietary_preference.dart';
import '../models/ingredient.dart';
import 'storage_service.dart';

class MealPlannerService extends ChangeNotifier {
  List<Recipe> _recipes = [];
  List<MealPlan> _mealPlans = [];
  List<GroceryItem> _groceryItems = [];
  DietaryPreference? _dietaryPreference;
  bool _isLoading = false;
  String? _error;

  final Uuid _uuid = const Uuid();

  // Getters
  List<Recipe> get recipes => List.unmodifiable(_recipes);
  List<MealPlan> get mealPlans => List.unmodifiable(_mealPlans);
  List<GroceryItem> get groceryItems => List.unmodifiable(_groceryItems);
  DietaryPreference? get dietaryPreference => _dietaryPreference;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize the service
  Future<void> initialize() async {
    _setLoading(true);
    try {
      await StorageService.init();
      await _loadData();
      
      // If first launch, add some sample data
      if (StorageService.isFirstLaunch) {
        await _addSampleData();
        await StorageService.setFirstLaunchComplete();
      }
    } catch (e) {
      _setError('Failed to initialize: $e');
    } finally {
      _setLoading(false);
    }
  }

  Future<void> _loadData() async {
    _recipes = await StorageService.getRecipes();
    _mealPlans = await StorageService.getMealPlans();
    _groceryItems = await StorageService.getGroceryItems();
    _dietaryPreference = await StorageService.getDietaryPreference();
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Recipe operations
  Future<void> addRecipe(Recipe recipe) async {
    try {
      await StorageService.addRecipe(recipe);
      _recipes.add(recipe);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add recipe: $e');
    }
  }

  Future<void> updateRecipe(Recipe recipe) async {
    try {
      await StorageService.updateRecipe(recipe);
      final index = _recipes.indexWhere((r) => r.id == recipe.id);
      if (index != -1) {
        _recipes[index] = recipe;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update recipe: $e');
    }
  }

  Future<void> deleteRecipe(String recipeId) async {
    try {
      await StorageService.deleteRecipe(recipeId);
      _recipes.removeWhere((r) => r.id == recipeId);
      
      // Remove from meal plans
      for (int i = 0; i < _mealPlans.length; i++) {
        final mealPlan = _mealPlans[i];
        bool updated = false;
        
        if (mealPlan.breakfast?.id == recipeId) {
          _mealPlans[i] = mealPlan.copyWith(breakfast: null);
          updated = true;
        }
        if (mealPlan.lunch?.id == recipeId) {
          _mealPlans[i] = mealPlan.copyWith(lunch: null);
          updated = true;
        }
        if (mealPlan.dinner?.id == recipeId) {
          _mealPlans[i] = mealPlan.copyWith(dinner: null);
          updated = true;
        }
        
        final newSnacks = mealPlan.snacks.where((s) => s.id != recipeId).toList();
        if (newSnacks.length != mealPlan.snacks.length) {
          _mealPlans[i] = _mealPlans[i].copyWith(snacks: newSnacks);
          updated = true;
        }
        
        if (updated) {
          await StorageService.addMealPlan(_mealPlans[i]);
        }
      }
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete recipe: $e');
    }
  }

  // Meal plan operations
  Future<void> addOrUpdateMealPlan(MealPlan mealPlan) async {
    try {
      await StorageService.addMealPlan(mealPlan);
      final index = _mealPlans.indexWhere((mp) => mp.id == mealPlan.id);
      if (index != -1) {
        _mealPlans[index] = mealPlan;
      } else {
        _mealPlans.add(mealPlan);
      }
      notifyListeners();
    } catch (e) {
      _setError('Failed to save meal plan: $e');
    }
  }

  Future<void> setMealForDate(DateTime date, MealType mealType, Recipe? recipe) async {
    final dateKey = _getDateKey(date);
    MealPlan? existingPlan = _mealPlans.firstWhere(
      (mp) => mp.id == dateKey,
      orElse: () => MealPlan(
        id: dateKey,
        date: date,
      ),
    );

    final updatedPlan = existingPlan.setMeal(mealType, recipe);
    await addOrUpdateMealPlan(updatedPlan);
  }

  MealPlan? getMealPlanForDate(DateTime date) {
    final dateKey = _getDateKey(date);
    try {
      return _mealPlans.firstWhere((mp) => mp.id == dateKey);
    } catch (e) {
      return null;
    }
  }

  String _getDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Grocery operations
  Future<void> addGroceryItem(GroceryItem item) async {
    try {
      await StorageService.addGroceryItem(item);
      _groceryItems.add(item);
      notifyListeners();
    } catch (e) {
      _setError('Failed to add grocery item: $e');
    }
  }

  Future<void> updateGroceryItem(GroceryItem item) async {
    try {
      await StorageService.updateGroceryItem(item);
      final index = _groceryItems.indexWhere((gi) => gi.id == item.id);
      if (index != -1) {
        _groceryItems[index] = item;
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to update grocery item: $e');
    }
  }

  Future<void> deleteGroceryItem(String itemId) async {
    try {
      await StorageService.deleteGroceryItem(itemId);
      _groceryItems.removeWhere((gi) => gi.id == itemId);
      notifyListeners();
    } catch (e) {
      _setError('Failed to delete grocery item: $e');
    }
  }

  Future<void> toggleGroceryItemCompleted(String itemId) async {
    final item = _groceryItems.firstWhere((gi) => gi.id == itemId);
    final updatedItem = item.copyWith(isCompleted: !item.isCompleted);
    await updateGroceryItem(updatedItem);
  }

  // Dietary preference operations
  Future<void> saveDietaryPreference(DietaryPreference preference) async {
    try {
      await StorageService.saveDietaryPreference(preference);
      _dietaryPreference = preference;
      notifyListeners();
    } catch (e) {
      _setError('Failed to save dietary preference: $e');
    }
  }

  // Generate grocery list from meal plans
  Future<void> generateGroceryListFromMealPlans(List<DateTime> dates) async {
    final Map<String, GroceryItem> groceryMap = {};
    
    for (final date in dates) {
      final mealPlan = getMealPlanForDate(date);
      if (mealPlan != null) {
        for (final meal in mealPlan.allMeals) {
          for (final ingredient in meal.ingredients) {
            final key = '${ingredient.name}_${ingredient.unit}';
            if (groceryMap.containsKey(key)) {
              final existing = groceryMap[key]!;
              groceryMap[key] = existing.copyWith(
                quantity: existing.quantity + ingredient.quantity,
                recipeIds: [...existing.recipeIds, meal.id],
              );
            } else {
              groceryMap[key] = GroceryItem(
                id: _uuid.v4(),
                name: ingredient.name,
                category: ingredient.category,
                quantity: ingredient.quantity,
                unit: ingredient.unit,
                createdAt: DateTime.now(),
                recipeIds: [meal.id],
              );
            }
          }
        }
      }
    }

    // Add new grocery items
    for (final item in groceryMap.values) {
      await addGroceryItem(item);
    }
  }

  Future<void> _addSampleData() async {
    // Add sample recipes
    final sampleRecipes = _createSampleRecipes();
    for (final recipe in sampleRecipes) {
      await StorageService.addRecipe(recipe);
    }
    _recipes.addAll(sampleRecipes);
    notifyListeners();
  }

  List<Recipe> _createSampleRecipes() {
    return [
      Recipe(
        id: _uuid.v4(),
        name: 'Scrambled Eggs',
        description: 'Simple and delicious scrambled eggs',
        ingredients: [
          Ingredient(
            id: _uuid.v4(),
            name: 'Eggs',
            category: 'Dairy',
            unit: 'pieces',
            quantity: 3,
            calories: 70,
          ),
          Ingredient(
            id: _uuid.v4(),
            name: 'Butter',
            category: 'Dairy',
            unit: 'tbsp',
            quantity: 1,
            calories: 100,
          ),
        ],
        instructions: [
          'Crack eggs into a bowl',
          'Whisk eggs with salt and pepper',
          'Heat butter in pan',
          'Add eggs and scramble gently',
          'Serve hot'
        ],
        preparationTime: 5,
        cookingTime: 5,
        servings: 1,
        mealType: MealType.breakfast,
        tags: ['quick', 'easy', 'protein'],
        createdAt: DateTime.now(),
      ),
      Recipe(
        id: _uuid.v4(),
        name: 'Chicken Salad',
        description: 'Fresh and healthy chicken salad',
        ingredients: [
          Ingredient(
            id: _uuid.v4(),
            name: 'Chicken Breast',
            category: 'Meat',
            unit: 'grams',
            quantity: 200,
            calories: 165,
          ),
          Ingredient(
            id: _uuid.v4(),
            name: 'Mixed Greens',
            category: 'Vegetables',
            unit: 'cups',
            quantity: 2,
            calories: 10,
          ),
        ],
        instructions: [
          'Cook chicken breast',
          'Slice chicken',
          'Mix with greens',
          'Add dressing',
          'Serve fresh'
        ],
        preparationTime: 15,
        cookingTime: 20,
        servings: 1,
        mealType: MealType.lunch,
        tags: ['healthy', 'protein', 'salad'],
        createdAt: DateTime.now(),
      ),
    ];
  }
}
