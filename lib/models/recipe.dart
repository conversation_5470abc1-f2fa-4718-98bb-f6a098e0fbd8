import 'ingredient.dart';

enum MealType { breakfast, lunch, dinner, snack }

class Recipe {
  final String id;
  final String name;
  final String description;
  final List<Ingredient> ingredients;
  final List<String> instructions;
  final int preparationTime; // in minutes
  final int cookingTime; // in minutes
  final int servings;
  final MealType mealType;
  final List<String> tags; // e.g., 'vegetarian', 'gluten-free', 'quick'
  final String? imageUrl;
  final double? rating;
  final DateTime createdAt;
  final DateTime? lastUsed;
  final int usageCount;

  const Recipe({
    required this.id,
    required this.name,
    required this.description,
    required this.ingredients,
    required this.instructions,
    required this.preparationTime,
    required this.cookingTime,
    required this.servings,
    required this.mealType,
    required this.tags,
    this.imageUrl,
    this.rating,
    required this.createdAt,
    this.lastUsed,
    this.usageCount = 0,
  });

  int get totalTime => preparationTime + cookingTime;

  double get totalCalories {
    return ingredients.fold(0.0, (sum, ingredient) {
      return sum + (ingredient.calories ?? 0) * ingredient.quantity;
    });
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'ingredients': ingredients.map((i) => i.toJson()).toList(),
      'instructions': instructions,
      'preparationTime': preparationTime,
      'cookingTime': cookingTime,
      'servings': servings,
      'mealType': mealType.name,
      'tags': tags,
      'imageUrl': imageUrl,
      'rating': rating,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed?.toIso8601String(),
      'usageCount': usageCount,
    };
  }

  factory Recipe.fromJson(Map<String, dynamic> json) {
    return Recipe(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      ingredients: (json['ingredients'] as List)
          .map((i) => Ingredient.fromJson(i as Map<String, dynamic>))
          .toList(),
      instructions: List<String>.from(json['instructions'] as List),
      preparationTime: json['preparationTime'] as int,
      cookingTime: json['cookingTime'] as int,
      servings: json['servings'] as int,
      mealType: MealType.values.firstWhere(
        (e) => e.name == json['mealType'],
        orElse: () => MealType.dinner,
      ),
      tags: List<String>.from(json['tags'] as List),
      imageUrl: json['imageUrl'] as String?,
      rating: json['rating'] != null ? (json['rating'] as num).toDouble() : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUsed: json['lastUsed'] != null 
          ? DateTime.parse(json['lastUsed'] as String) 
          : null,
      usageCount: json['usageCount'] as int? ?? 0,
    );
  }

  Recipe copyWith({
    String? id,
    String? name,
    String? description,
    List<Ingredient>? ingredients,
    List<String>? instructions,
    int? preparationTime,
    int? cookingTime,
    int? servings,
    MealType? mealType,
    List<String>? tags,
    String? imageUrl,
    double? rating,
    DateTime? createdAt,
    DateTime? lastUsed,
    int? usageCount,
  }) {
    return Recipe(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      preparationTime: preparationTime ?? this.preparationTime,
      cookingTime: cookingTime ?? this.cookingTime,
      servings: servings ?? this.servings,
      mealType: mealType ?? this.mealType,
      tags: tags ?? this.tags,
      imageUrl: imageUrl ?? this.imageUrl,
      rating: rating ?? this.rating,
      createdAt: createdAt ?? this.createdAt,
      lastUsed: lastUsed ?? this.lastUsed,
      usageCount: usageCount ?? this.usageCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Recipe(id: $id, name: $name, mealType: $mealType)';
  }
}
