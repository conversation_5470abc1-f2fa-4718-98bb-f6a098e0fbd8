import 'recipe.dart';

class MealPlan {
  final String id;
  final DateTime date;
  final Recipe? breakfast;
  final Recipe? lunch;
  final Recipe? dinner;
  final List<Recipe> snacks;
  final String? notes;

  const MealPlan({
    required this.id,
    required this.date,
    this.breakfast,
    this.lunch,
    this.dinner,
    this.snacks = const [],
    this.notes,
  });

  bool get isEmpty => breakfast == null && lunch == null && dinner == null && snacks.isEmpty;

  double get totalCalories {
    double total = 0;
    if (breakfast != null) total += breakfast!.totalCalories;
    if (lunch != null) total += lunch!.totalCalories;
    if (dinner != null) total += dinner!.totalCalories;
    for (final snack in snacks) {
      total += snack.totalCalories;
    }
    return total;
  }

  List<Recipe> get allMeals {
    final meals = <Recipe>[];
    if (breakfast != null) meals.add(breakfast!);
    if (lunch != null) meals.add(lunch!);
    if (dinner != null) meals.add(dinner!);
    meals.addAll(snacks);
    return meals;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'breakfast': breakfast?.toJson(),
      'lunch': lunch?.toJson(),
      'dinner': dinner?.toJson(),
      'snacks': snacks.map((s) => s.toJson()).toList(),
      'notes': notes,
    };
  }

  factory MealPlan.fromJson(Map<String, dynamic> json) {
    return MealPlan(
      id: json['id'] as String,
      date: DateTime.parse(json['date'] as String),
      breakfast: json['breakfast'] != null 
          ? Recipe.fromJson(json['breakfast'] as Map<String, dynamic>)
          : null,
      lunch: json['lunch'] != null 
          ? Recipe.fromJson(json['lunch'] as Map<String, dynamic>)
          : null,
      dinner: json['dinner'] != null 
          ? Recipe.fromJson(json['dinner'] as Map<String, dynamic>)
          : null,
      snacks: json['snacks'] != null
          ? (json['snacks'] as List)
              .map((s) => Recipe.fromJson(s as Map<String, dynamic>))
              .toList()
          : [],
      notes: json['notes'] as String?,
    );
  }

  MealPlan copyWith({
    String? id,
    DateTime? date,
    Recipe? breakfast,
    Recipe? lunch,
    Recipe? dinner,
    List<Recipe>? snacks,
    String? notes,
  }) {
    return MealPlan(
      id: id ?? this.id,
      date: date ?? this.date,
      breakfast: breakfast ?? this.breakfast,
      lunch: lunch ?? this.lunch,
      dinner: dinner ?? this.dinner,
      snacks: snacks ?? this.snacks,
      notes: notes ?? this.notes,
    );
  }

  MealPlan setMeal(MealType mealType, Recipe? recipe) {
    switch (mealType) {
      case MealType.breakfast:
        return copyWith(breakfast: recipe);
      case MealType.lunch:
        return copyWith(lunch: recipe);
      case MealType.dinner:
        return copyWith(dinner: recipe);
      case MealType.snack:
        if (recipe != null) {
          return copyWith(snacks: [...snacks, recipe]);
        } else {
          return this;
        }
    }
  }

  MealPlan removeMeal(MealType mealType, {Recipe? snackToRemove}) {
    switch (mealType) {
      case MealType.breakfast:
        return copyWith(breakfast: null);
      case MealType.lunch:
        return copyWith(lunch: null);
      case MealType.dinner:
        return copyWith(dinner: null);
      case MealType.snack:
        if (snackToRemove != null) {
          final newSnacks = snacks.where((s) => s.id != snackToRemove.id).toList();
          return copyWith(snacks: newSnacks);
        }
        return this;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MealPlan && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MealPlan(id: $id, date: $date, meals: ${allMeals.length})';
  }
}
