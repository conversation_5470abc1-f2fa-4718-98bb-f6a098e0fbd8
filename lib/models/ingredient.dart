class Ingredient {
  final String id;
  final String name;
  final String category;
  final String unit; // e.g., 'cups', 'grams', 'pieces'
  final double quantity;
  final double? calories; // per unit
  final Map<String, double>? nutrition; // protein, carbs, fat, etc.

  const Ingredient({
    required this.id,
    required this.name,
    required this.category,
    required this.unit,
    required this.quantity,
    this.calories,
    this.nutrition,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'unit': unit,
      'quantity': quantity,
      'calories': calories,
      'nutrition': nutrition,
    };
  }

  factory Ingredient.fromJson(Map<String, dynamic> json) {
    return Ingredient(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      unit: json['unit'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      calories: json['calories'] != null ? (json['calories'] as num).toDouble() : null,
      nutrition: json['nutrition'] != null 
          ? Map<String, double>.from(json['nutrition'] as Map)
          : null,
    );
  }

  Ingredient copyWith({
    String? id,
    String? name,
    String? category,
    String? unit,
    double? quantity,
    double? calories,
    Map<String, double>? nutrition,
  }) {
    return Ingredient(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      unit: unit ?? this.unit,
      quantity: quantity ?? this.quantity,
      calories: calories ?? this.calories,
      nutrition: nutrition ?? this.nutrition,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Ingredient && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Ingredient(id: $id, name: $name, quantity: $quantity $unit)';
  }
}
