class GroceryItem {
  final String id;
  final String name;
  final String category;
  final double quantity;
  final String unit;
  final bool isCompleted;
  final double? estimatedPrice;
  final String? notes;
  final DateTime createdAt;
  final List<String> recipeIds; // Which recipes need this ingredient

  const GroceryItem({
    required this.id,
    required this.name,
    required this.category,
    required this.quantity,
    required this.unit,
    this.isCompleted = false,
    this.estimatedPrice,
    this.notes,
    required this.createdAt,
    this.recipeIds = const [],
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'quantity': quantity,
      'unit': unit,
      'isCompleted': isCompleted,
      'estimatedPrice': estimatedPrice,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'recipeIds': recipeIds,
    };
  }

  factory GroceryItem.fromJson(Map<String, dynamic> json) {
    return GroceryItem(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unit: json['unit'] as String,
      isCompleted: json['isCompleted'] as bool? ?? false,
      estimatedPrice: json['estimatedPrice'] != null 
          ? (json['estimatedPrice'] as num).toDouble() 
          : null,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      recipeIds: List<String>.from(json['recipeIds'] as List? ?? []),
    );
  }

  GroceryItem copyWith({
    String? id,
    String? name,
    String? category,
    double? quantity,
    String? unit,
    bool? isCompleted,
    double? estimatedPrice,
    String? notes,
    DateTime? createdAt,
    List<String>? recipeIds,
  }) {
    return GroceryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      isCompleted: isCompleted ?? this.isCompleted,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      recipeIds: recipeIds ?? this.recipeIds,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GroceryItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'GroceryItem(id: $id, name: $name, quantity: $quantity $unit, completed: $isCompleted)';
  }
}
