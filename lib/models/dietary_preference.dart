enum DietaryRestriction {
  vegetarian,
  vegan,
  glutenFree,
  dairyFree,
  nutFree,
  lowCarb,
  lowFat,
  lowSodium,
  keto,
  paleo,
  mediterranean,
  diabetic,
  halal,
  kosher,
}

class DietaryPreference {
  final String id;
  final String userId; // For future multi-user support
  final List<DietaryRestriction> restrictions;
  final List<String> allergies; // Custom allergens
  final List<String> dislikedIngredients;
  final List<String> preferredIngredients;
  final int? dailyCalorieGoal;
  final Map<String, double>? macroGoals; // protein, carbs, fat percentages
  final DateTime createdAt;
  final DateTime updatedAt;

  const DietaryPreference({
    required this.id,
    required this.userId,
    this.restrictions = const [],
    this.allergies = const [],
    this.dislikedIngredients = const [],
    this.preferredIngredients = const [],
    this.dailyCalorieGoal,
    this.macroGoals,
    required this.createdAt,
    required this.updatedAt,
  });

  bool isRecipeCompatible(List<String> recipeTags, List<String> recipeIngredients) {
    // Check dietary restrictions
    for (final restriction in restrictions) {
      final restrictionTag = restriction.name.toLowerCase();
      if (!recipeTags.any((tag) => tag.toLowerCase().contains(restrictionTag))) {
        // If recipe doesn't have the required tag, it might not be compatible
        // This is a simplified check - in reality, you'd want more sophisticated logic
        switch (restriction) {
          case DietaryRestriction.vegetarian:
            if (recipeIngredients.any((ingredient) => 
                _meatIngredients.any((meat) => 
                    ingredient.toLowerCase().contains(meat.toLowerCase())))) {
              return false;
            }
            break;
          case DietaryRestriction.vegan:
            if (recipeIngredients.any((ingredient) => 
                _animalProducts.any((animal) => 
                    ingredient.toLowerCase().contains(animal.toLowerCase())))) {
              return false;
            }
            break;
          case DietaryRestriction.glutenFree:
            if (recipeIngredients.any((ingredient) => 
                _glutenIngredients.any((gluten) => 
                    ingredient.toLowerCase().contains(gluten.toLowerCase())))) {
              return false;
            }
            break;
          // Add more restriction checks as needed
          default:
            break;
        }
      }
    }

    // Check allergies
    for (final allergy in allergies) {
      if (recipeIngredients.any((ingredient) => 
          ingredient.toLowerCase().contains(allergy.toLowerCase()))) {
        return false;
      }
    }

    // Check disliked ingredients
    for (final disliked in dislikedIngredients) {
      if (recipeIngredients.any((ingredient) => 
          ingredient.toLowerCase().contains(disliked.toLowerCase()))) {
        return false;
      }
    }

    return true;
  }

  static const List<String> _meatIngredients = [
    'beef', 'chicken', 'pork', 'lamb', 'turkey', 'fish', 'salmon', 'tuna', 
    'shrimp', 'crab', 'lobster', 'bacon', 'ham', 'sausage'
  ];

  static const List<String> _animalProducts = [
    'beef', 'chicken', 'pork', 'lamb', 'turkey', 'fish', 'salmon', 'tuna', 
    'shrimp', 'crab', 'lobster', 'bacon', 'ham', 'sausage', 'milk', 'cheese', 
    'butter', 'cream', 'yogurt', 'egg', 'honey'
  ];

  static const List<String> _glutenIngredients = [
    'wheat', 'flour', 'bread', 'pasta', 'barley', 'rye', 'oats'
  ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'restrictions': restrictions.map((r) => r.name).toList(),
      'allergies': allergies,
      'dislikedIngredients': dislikedIngredients,
      'preferredIngredients': preferredIngredients,
      'dailyCalorieGoal': dailyCalorieGoal,
      'macroGoals': macroGoals,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory DietaryPreference.fromJson(Map<String, dynamic> json) {
    return DietaryPreference(
      id: json['id'] as String,
      userId: json['userId'] as String,
      restrictions: (json['restrictions'] as List?)
          ?.map((r) => DietaryRestriction.values.firstWhere(
              (e) => e.name == r,
              orElse: () => DietaryRestriction.vegetarian))
          .toList() ?? [],
      allergies: List<String>.from(json['allergies'] as List? ?? []),
      dislikedIngredients: List<String>.from(json['dislikedIngredients'] as List? ?? []),
      preferredIngredients: List<String>.from(json['preferredIngredients'] as List? ?? []),
      dailyCalorieGoal: json['dailyCalorieGoal'] as int?,
      macroGoals: json['macroGoals'] != null 
          ? Map<String, double>.from(json['macroGoals'] as Map)
          : null,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  DietaryPreference copyWith({
    String? id,
    String? userId,
    List<DietaryRestriction>? restrictions,
    List<String>? allergies,
    List<String>? dislikedIngredients,
    List<String>? preferredIngredients,
    int? dailyCalorieGoal,
    Map<String, double>? macroGoals,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DietaryPreference(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      restrictions: restrictions ?? this.restrictions,
      allergies: allergies ?? this.allergies,
      dislikedIngredients: dislikedIngredients ?? this.dislikedIngredients,
      preferredIngredients: preferredIngredients ?? this.preferredIngredients,
      dailyCalorieGoal: dailyCalorieGoal ?? this.dailyCalorieGoal,
      macroGoals: macroGoals ?? this.macroGoals,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DietaryPreference && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'DietaryPreference(id: $id, restrictions: $restrictions)';
  }
}
